# 财务报表

## 月度营收 (Monthly Revenue)

营收只来自于消耗的付费积分。

```sql
-- 月度营收（单位：元）
SELECT 
    -- 消耗的付费积分是负数，所以要取反再求和
    SUM(-paid_points_change) / 1000.0 AS "月度营收_元"
FROM 
    transactions
WHERE 
    source = 'CONSUMPTION'
    AND paid_points_change < 0 -- 只统计消耗付费积分的交易
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 月度用户增长 (Monthly User Growth)

```sql
-- 月度用户增长
SELECT 
    COUNT(DISTINCT user_id) AS "月度用户增长_人"
FROM 
    transactions
WHERE 
    source = 'GIFT_SIGNUP'
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 月度充值总额 (Total Recharges)

```sql
-- 月度充值总额（单位：元）
SELECT 
    SUM(amount) AS "月度充值总额_元"
FROM 
    recharge_requests
WHERE 
    status = 'approved'
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 营销成本 - 赠送积分消耗 (Marketing Cost)

```sql
-- 营销成本（单位：元）
SELECT 
    SUM(free_points_change) / 1000.0 AS "营销成本_元"
FROM 
    transactions
WHERE 
    source = 'CONSUMPTION'
    AND free_points_change < 0 -- 只统计消耗赠送积分的交易
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
```

## 预收账款变化 (Change in Deferred Revenue)

预收账款 = 所有用户的 paid_balance 之和。月度变化 = 月度新增付费积分 - 月度消耗付费积分。

```sql
-- 月度预收账款净变化（单位：元）
SELECT
    SUM(paid_points_change) / 1000.0 AS "预收账款净变化_元"
FROM
    transactions
WHERE
    -- 考虑充值(增加)和消费(减少)
    source IN ('RECHARGE', 'CONSUMPTION') 
    AND created_at >= '2023-10-01' 
    AND created_at < '2023-11-01';
-- 正数表示预收账款增加，负数表示减少
```

## 综合月度报表 (Comprehensive Monthly Report)

包括:

- 营收
- 充值额
- 消耗的总积分 (付费+赠送)
- 营销成本 (本月送出总积分)
- 预收账款净变化
