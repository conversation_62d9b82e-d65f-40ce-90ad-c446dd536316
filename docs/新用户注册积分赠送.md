# 新用户注册积分赠送功能

## 功能概述

当新用户注册时，系统会自动为其创建钱包并赠送500积分。这个功能通过检查钱包是否存在来判断是否是新用户，如果钱包不存在则认为是新用户，然后创建钱包并赠送积分。

## 实现方案

### 1. 数据库结构支持

系统支持两种数据库结构：

#### 旧结构（当前使用）
- `wallets` 表：`balance` 字段存储总积分
- `transactions` 表：`type` 字段区分交易类型，`amount` 字段存储积分变化

#### 新结构（优化版本）
- `wallets` 表：`paid_balance` 和 `free_balance` 分别存储付费积分和赠送积分
- `transactions` 表：`source` 字段标识交易来源，`paid_points_change` 和 `free_points_change` 分别记录两种积分的变化

### 2. 核心方法

#### `CreateNewUserWallet(userID string) error`
专门用于新用户注册时创建钱包并赠送积分：
- 检查钱包是否已存在，如果存在则直接返回
- 自动检测数据库结构（新/旧）
- 在事务中创建钱包和交易记录
- 新结构：`paid_balance=0, free_balance=500`
- 旧结构：`balance=500`

#### `EnsureWalletExists(userID uuid.UUID) error`
确保用户钱包存在，如果不存在则创建：
- 用于登录时检查钱包
- 功能与 `CreateNewUserWallet` 类似，但参数类型不同

### 3. 集成点

#### 用户注册 (`auth.go`)
```go
// Register 方法中添加
err = pg.CreateNewUserWallet(res.User.ID.String())
if err != nil {
    LogError("为新用户创建钱包失败: %v", err)
    // 注册成功但钱包创建失败，仍然返回成功，钱包会在首次登录时创建
}
```

#### 管理员创建用户 (`user.go`)
```go
// AdminCreateUser 方法中添加
err = pg.CreateNewUserWallet(res.ID.String())
if err != nil {
    LogError("为新用户创建钱包失败: %v", err)
    // 用户创建成功但钱包创建失败，仍然返回成功，钱包会在首次登录时创建
}
```

#### 用户登录 (`auth.go`)
```go
// Login 方法中已有
err = pg.EnsureWalletExists(resp.User.ID)
if err!= nil {
    return nil, fmt.Errorf("登录失败: %v", err)
}
```

### 4. 交易记录

#### 新结构交易记录
- `source`: 'GIFT_SIGNUP'
- `paid_points_change`: 0
- `free_points_change`: 500
- `paid_balance_after`: 0
- `free_balance_after`: 500
- `description`: 'New user registration gift: 500 points'

#### 旧结构交易记录
- `type`: 'deposit'
- `amount`: 500
- `balance_after`: 500
- `description`: 'New user registration gift: 500 points'

### 5. 错误处理

- 钱包创建失败不会影响用户注册/创建的成功
- 错误会被记录到日志中
- 如果注册时钱包创建失败，用户首次登录时会重新尝试创建

### 6. 数据一致性

- 所有钱包和交易操作都在数据库事务中执行
- 确保钱包创建和交易记录的原子性
- 支持并发安全的钱包检查和创建

## 测试

可以使用 `test_new_user_wallet.go` 文件进行功能测试：

```bash
go run test_new_user_wallet.go
```

测试包括：
1. 新用户钱包创建功能
2. 钱包存在性检查功能
3. 余额验证
4. 交易记录验证

## 注意事项

1. 系统会自动检测数据库结构，无需手动配置
2. 赠送积分数量固定为500，如需修改请更新代码中的常量
3. 钱包创建失败不会阻止用户注册，确保用户体验
4. 所有操作都有详细的错误日志记录
