# 自部署开源轻量级日志系统(Loki)

---

## 首选推荐：Grafana Loki

**Loki** 是由 Grafana Labs 开发的日志聚合系统。它的设计哲学是：“像 Prometheus 一样，但用于日志”。

- **为什么它很轻量？** Loki 不会对日志的全文内容进行索引。它只索引与日志流关联的元数据（称为“标签”，如 `app="cloud-function"`, `level="error"`）。这使得它的内存和存储需求极低，非常适合在轻量服务器上运行。
- **有什么功能？**
  - 通过 Grafana 提供强大、美观的日志查询和可视化 UI。
  - 使用 LogQL 查询语言，可以像 `grep` 一样轻松过滤和查询日志。
  - 可以与 Prometheus 指标无缝关联。
  - 部署非常简单，尤其是使用 Docker。

### 如何在轻量服务器上用 Docker 部署 Loki + Promtail + Grafana

这个组合是黄金搭档：

- **Loki**: 负责存储日志。
- **Promtail**: 是日志代理（采集器）。它可以监听一个 HTTP 端口接收日志，也可以抓取本地文件（比如你的 PostgreSQL 日志！）。
- **Grafana**: 用于查询和展示日志的 UI。

#### 部署步骤

##### 1. 在轻量服务器上安装 Docker 和 Docker Compose

如果尚未安装，请执行：

```bash
# 安装 Docker
sudo apt-get update
sudo apt-get install -y docker.io

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

##### 2. 创建项目目录和配置文件

```bash
mkdir ~/loki-stack && cd ~/loki-stack
touch docker-compose.yml
mkdir -p config
touch config/loki-config.yml
touch config/promtail-config.yml
```

##### 3. 编辑 `docker-compose.yml`

这是核心文件，用于定义和运行三个服务。

```yaml
services:
  loki:
    image: grafana/loki:3.5.1
    container_name: loki
    command: -config.file=/etc/loki/loki-config.yml
    ports:
      - "3100:3100" # Loki API 端口
    volumes:
      - ./config:/etc/loki
      - loki_data:/loki # 持久化存储日志数据
    restart: unless-stopped

  promtail:
    image: grafana/promtail:3.5.1
    container_name: promtail
    command: -config.file=/etc/promtail/promtail-config.yml
    ports:
      - "9081:9081"
    volumes:
      - ./config:/etc/promtail
      - /var/log:/var/log # 将主机的/var/log目录映射进来，以便promtail可以读取
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000" # Grafana Web UI 端口
    volumes:
      - grafana_data:/var/lib/grafana # 持久化存储Grafana配置和仪表盘
    restart: unless-stopped

volumes:
  loki_data:
  grafana_data:
```

##### 4. 编辑 Loki 配置文件 `config/loki-config.yml`

这是一个非常基础的配置，将日志存储在本地文件系统。

```yaml
auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096
  log_level: debug
  grpc_server_max_concurrent_streams: 1000

common:
  instance_addr: 127.0.0.1
  path_prefix: /tmp/loki
  storage:
    filesystem:
      chunks_directory: /tmp/loki/chunks
      rules_directory: /tmp/loki/rules
  replication_factor: 1
  ring:
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

limits_config:
  metric_aggregation_enabled: true
  enable_multi_variant_queries: true

schema_config:
  configs:
    - from: 2020-10-24
      store: tsdb
      object_store: filesystem
      schema: v13
      index:
        prefix: index_
        period: 24h

pattern_ingester:
  enabled: true
  metric_aggregation:
    loki_address: localhost:3100

ruler:
  alertmanager_url: http://localhost:9093

frontend:
  encoding: protobuf


# By default, Loki will send anonymous, but uniquely-identifiable usage and configuration
# analytics to Grafana Labs. These statistics are sent to https://stats.grafana.org/
#
# Statistics help us better understand how Loki is used, and they show us performance
# levels for most users. This helps us prioritize features and documentation.
# For more information on what's sent, look at
# https://github.com/grafana/loki/blob/main/pkg/analytics/stats.go
# Refer to the buildReport method to see what goes into a report.
#
# If you would like to disable reporting, uncomment the following lines:
analytics:
  reporting_enabled: false
```

##### 5. 编辑 Promtail 配置文件 `config/promtail-config.yml`

这是关键配置！我们让 Promtail 做两件事：
a. 监听一个 HTTP 端口，接收来自云函数的日志。
b. 主动抓取 PostgreSQL 的日志文件。

```yaml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # (A) 接收来自云函数的日志
  - job_name: cloud-function
    # 使用 Loki Push API 接收日志
    loki_push_api:
      server:
        http_listen_port: 9081 # 我们让云函数把日志发到这个端口
      # 定义标签，所有发到这里的日志都会自动带上这些标签
      labels:
        job: cloud-function-push
        app: aig-function

  # (B) (可选) 抓取 PostgreSQL 的日志文件
  - job_name: postgres
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgres
          app: aig-postgres
          __path__: /var/log/postgresql/postgresql-17-main.log # 根据你实际的PG日志路径修改
```

##### 6. 启动服务

```bash
# 在 loki-stack 目录下执行
docker-compose up -d
```

现在，Loki (`3100`), Grafana (`3000`), Promtail (`9080`, `9081`) 都在后台运行了。

##### 7. 配置防火墙

在你的轻量服务器防火墙中，放行以下端口：

- `3000/TCP`：用于你从浏览器访问 Grafana UI (来源可以是你的办公 IP，或者 `0.0.0.0/0` 如果你想公开访问)。
- `9081/TCP`：用于云函数通过内网访问 Promtail (来源应设置为你的**VPC 网段**，例如 `10.0.0.0/16`)。

##### 8. 修改云函数代码，将日志发送到 Promtail

你的 Go 云函数日志客户端需要做一些小小的修改。Loki 的 Push API 需要一个特定的 JSON 格式。

```go
// logger.go in your cloud function

package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"
)

// LokiStream represents a single log stream
type LokiStream struct {
	Stream map[string]string `json:"stream"`
	Values [][]string        `json:"values"`
}

// LokiPayload is the top-level structure for the push API
type LokiPayload struct {
	Streams []LokiStream `json:"streams"`
}

var (
	// 从环境变量获取配置
	logServerURL = os.Getenv("LOG_SERVER_URL") // 现在是 Promtail 的地址: http://*********:9081/loki/api/v1/push
	httpClient   = &http.Client{Timeout: 3 * time.Second}
)

// SendLogToLoki 发送日志到Loki/Promtail
func SendLogToLoki(ctx context.Context, level, message string, data map[string]interface{}) {
	if logServerURL == "" {
		fmt.Printf("Fallback Log: [%s] %s | Data: %+v\n", level, message, data)
		return
	}

    // 附加数据也变成JSON字符串
    dataBytes, _ := json.Marshal(data)
    fullMessage := fmt.Sprintf("%s data=%s", message, string(dataBytes))

	payload := LokiPayload{
		Streams: []LokiStream{
			{
				Stream: map[string]string{
					"level": level, // "level" 会成为 Loki 中的一个标签！
				},
				Values: [][]string{
					// [0]: 时间戳 (纳秒级), [1]: 日志行内容
					{fmt.Sprintf("%d", time.Now().UnixNano()), fullMessage},
				},
			},
		},
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		fmt.Printf("Error marshaling Loki payload: %v\n", err)
		return
	}

	req, err := http.NewRequestWithContext(ctx, "POST", logServerURL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		fmt.Printf("Error creating Loki request: %v\n", err)
		return
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := httpClient.Do(req)
	if err != nil {
		fmt.Printf("Error sending log to promtail: %v. Fallback Log: %s\n", err, fullMessage)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent { // Loki Push API 成功时返回 204 No Content
		fmt.Printf("Promtail returned status %d. Fallback Log: %s\n", resp.StatusCode, fullMessage)
	}
}

// 在你的主逻辑中调用
// SendLogToLoki(ctx, "error", "Failed to connect to DB", map[string]interface{}{"error": err.Error()})
```

使用http发送测试日志

POST

<http://*********:9081/loki/api/v1/push>

header

```text
Content-Type: application/json
```

body

```json
{
    "streams": [
      {
        "stream": {
          "job": "cloud-function-push",
          "app": "aig-function",
          "level": "debug",
          "source": "curl-test"
        },
        "values": [
          [ "1750668966", "Hello Loki! This is a test log from curl." ]
        ]
      }
    ]
  }
```

**云函数环境变量更新：**

- `LOG_SERVER_URL`: `http://<你的轻量服务器内网IP>:9081/loki/api/v1/push`

##### 9. 在 Grafana 中查看日志

1. 浏览器访问 `http://<你的轻量服务器公网IP>:3000`。
2. 默认用户名/密码是 `admin`/`admin` (首次登录会要求修改密码)。
3. 左侧菜单 -> Configuration -> Data Sources -> Add data source。
4. 选择 **Loki**。
5. 在 HTTP URL 字段中，填入 `http://loki:3100` (因为 Grafana 和 Loki 在同一个 Docker 网络中，可以直接用服务名访问)。
6. 点击 "Save & Test"，应该会显示 "Data source connected"。
7. 左侧菜单 -> Explore。
8. 在顶部选择 Loki 数据源。
9. 点击 "Log browser" 按钮，你应该能看到 `job` 和 `app` 等标签。选择 `job="cloud-function-push"`，你就能看到云函数发来的日志了！

---

### 其他轻量级备选方案

| 系统                     | 优点                                                                                                                                                                | 缺点                                                                                | 适合场景                                                               |
| :----------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :---------------------------------------------------------------------------------- | :--------------------------------------------------------------------- |
| **SigNoz**               | 开源的 **All-in-One** 可观测性平台（日志、指标、追踪）。部署相对简单。                                                                                              | 资源消耗比纯日志系统略高。功能可能超出你当前所需。                                  | 如果你未来有监控指标和分布式追踪的需求，可以一步到位。                 |
| **Fluentd / Fluent Bit** | 极其强大和灵活的日志**采集器**，插件生态非常丰富。                                                                                                                  | 它本身不是一个完整的系统，只负责采集和转发，仍需后端存储（如文件、S3、Loki）和 UI。 | 如果你有非常复杂的日志采集需求，例如从多种源头采集并发送到多个目的地。 |
| **Simple Log Viewer UI** | 极其轻量。你可以继续使用你之前写的 Go 日志服务（写入文件），然后部署一个简单的 Web UI 来读取日志文件。例如 [log-viewer](https://github.com/log-viewer/log-viewer)。 | 功能非常有限，通常只有实时查看（tail）和简单搜索。没有历史归档和高级查询。          | 对功能要求极低，只想有一个比 `tail -f` 稍微好用一点的 Web 界面。       |

### 结论与建议

对于你的场景，**Grafana Loki 栈是最佳选择**。

- **轻量级**：完美适配轻量应用服务器的资源限制。
- **功能强大**：提供了专业的日志查询和可视化界面，远超简单的文件写入。
- **部署简单**：通过 Docker Compose 可以一键部署。
- **可扩展**：不仅能接收云函数的日志，还能顺便监控服务器上的其他日志文件（如 PostgreSQL、Nginx 等），实现了日志的集中管理。

直接采用 Loki 方案，可以让你用最小的成本，获得一个准生产级的、功能完善的日志系统。
