-- 开启一个新的数据库事务
BEGIN;
-- 创建网站配置表
CREATE TABLE IF NOT EXISTS website_configs (
    -- 网站业务ID (如'dongni100')
    id TEXT PRIMARY KEY,
    -- 网站名称
    name TEXT NOT NULL,
    -- 网站URL
    url TEXT NOT NULL,
    -- 操作步骤数组 (JSON格式)
    actions JSONB NOT NULL
);
-- 系统提示词表
CREATE TABLE IF NOT EXISTS system_prompts (
    -- 自增ID
    id SERIAL PRIMARY KEY,
    -- 提示词的唯一标识符
    prompt_key TEXT NOT NULL UNIQUE,
    -- 提示词内容
    prompt_text TEXT NOT NULL,
    -- 提示词描述
    description TEXT,
    -- 提示词分类
    category TEXT,
    -- 创建时间戳
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    -- 更新时间戳
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
-- 添加UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- 充值状态枚举类型
CREATE TYPE IF NOT EXISTS approval_status AS ENUM ('pending', 'approved', 'rejected');
-- 充值申请表: recharge_requests
CREATE TABLE IF NOT EXISTS recharge_requests (
    -- 自增ID
    id SERIAL PRIMARY KEY,
    -- 对应于Supabase中的auth.users.id
    user_id UUID NOT NULL,
    -- 充值金额，单位：元
    amount NUMERIC(10, 2) NOT NULL,
    -- 这个字段依然非常有用，它将换算逻辑固定在充值申请创建时
    -- 避免了后续因为兑换比例变化导致的历史数据问题
    -- 根据充值金额换算出的积分
    points_to_grant INTEGER NOT NULL,
    -- 支付宝订单号，唯一键防止重复充值
    order_id TEXT NOT NULL UNIQUE,
    -- 充值状态: pending, approved, rejected
    status approval_status NOT NULL DEFAULT 'pending',
    -- 支付方式：默认支付宝
    payment_method TEXT NOT NULL DEFAULT 'alipay'::text,
    -- 支付凭证URL或路径
    payment_proof TEXT,
    -- 管理员处理备注
    admin_note TEXT,
    -- 处理此申请的管理员ID
    processed_by UUID NOT NULL,
    -- 处理时间,
    processed_at TIMESTAMPTZ,
    -- 创建时间戳
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    -- 更新时间戳
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
-- 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_recharge_requests_user_id ON recharge_requests(user_id);
-- 钱包表: wallets
-- 将单一余额拆分为付费和赠送余额
CREATE TABLE IF NOT EXISTS wallets (
    -- 自增ID
    id SERIAL PRIMARY KEY,
    -- 对应于Supabase中的auth.users.id
    user_id UUID NOT NULL UNIQUE,
    -- 付费积分余额
    paid_balance BIGINT NOT NULL DEFAULT 0,
    -- 赠送积分余额
    free_balance BIGINT NOT NULL DEFAULT 0,
    -- 总余额可以是一个视图或生成列，但为简化，在应用层计算 (paid_balance + free_balance)
    -- 创建时间戳
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    -- 更新时间戳
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
-- 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id);
-- 交易类型枚举
-- 增加更明确的交易来源
CREATE TYPE IF NOT EXISTS transaction_source AS ENUM (
    -- 付费充值
    'RECHARGE',
    -- 业务消费
    'CONSUMPTION',
    -- 注册赠送
    'GIFT_SIGNUP',
    -- 活动赠送
    'GIFT_PROMO',
    -- 退款
    'REFUND' 
);
-- 交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    -- 自增ID
    id BIGSERIAL PRIMARY KEY,
    -- 对应于Supabase中的auth.users.id
    user_id UUID NOT NULL,
    -- 交易来源/类型
    source transaction_source NOT NULL,
    -- 使用两个字段精确追踪每种积分的变化量
    -- 正数表示增加, 负数表示减少
    paid_points_change BIGINT NOT NULL DEFAULT 0,
    free_points_change BIGINT NOT NULL DEFAULT 0,
    -- 交易后的快照，用于快速对账和审计
    paid_balance_after BIGINT NOT NULL,
    free_balance_after BIGINT NOT NULL,
    -- 交易描述, e.g., "AI阅卷, 消耗300积分"
    description TEXT,
    -- 关联外部实体，增强可追溯性
    -- 关联充值申请
    related_recharge_id INTEGER REFERENCES recharge_requests(id) ON DELETE SET NULL,
    -- 关联的业务ID, e.g., 某次阅卷任务的ID
    related_consumption_id TEXT,
    -- 存储额外信息，使用JSONB类型性能更好
    metadata JSONB,
    -- 创建时间戳
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);
-- 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_transactions_user_id_created_at ON transactions(user_id, created_at);
-- 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_transactions_source ON transactions(source);
COMMIT;