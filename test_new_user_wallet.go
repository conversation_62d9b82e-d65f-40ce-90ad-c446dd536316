package main

import (
	"fmt"
	"os"
	"testing"

	"github.com/google/uuid"
)

// TestCreateNewUserWallet 测试新用户钱包创建功能
func TestCreateNewUserWallet(t *testing.T) {
	// 设置测试环境变量（如果需要）
	if os.Getenv("DB_HOST") == "" {
		t.<PERSON><PERSON>("跳过测试：未设置数据库环境变量")
	}

	// 初始化数据库操作
	pg := NewPostgreSQLOperations()
	defer pg.Close()

	// 生成测试用户ID
	testUserID := uuid.New().String()
	fmt.Printf("测试用户ID: %s\n", testUserID)

	// 测试创建新用户钱包
	err := pg.CreateNewUserWallet(testUserID)
	if err != nil {
		t.Fatalf("创建新用户钱包失败: %v", err)
	}

	// 验证钱包是否创建成功
	balance, err := pg.GetUserBalance(testUserID)
	if err != nil {
		t.Fatalf("获取用户余额失败: %v", err)
	}

	// 检查余额是否为500积分
	if balance != 500 {
		t.<PERSON><PERSON><PERSON>("期望余额为500积分，实际为%d积分", balance)
	}

	// 验证交易记录是否创建
	transactions, err := pg.GetUserTransactionRecords(testUserID, 10, 0)
	if err != nil {
		t.Fatalf("获取交易记录失败: %v", err)
	}

	if len(transactions) == 0 {
		t.Error("期望有交易记录，但没有找到")
	} else {
		transaction := transactions[0]
		if transaction.Amount != 500 {
			t.Errorf("期望交易金额为500积分，实际为%d积分", transaction.Amount)
		}
		if transaction.BalanceAfter != 500 {
			t.Errorf("期望交易后余额为500积分，实际为%d积分", transaction.BalanceAfter)
		}
	}

	fmt.Printf("测试通过：新用户钱包创建成功，余额为%d积分\n", balance)
}

// TestEnsureWalletExists 测试确保钱包存在功能
func TestEnsureWalletExists(t *testing.T) {
	// 设置测试环境变量（如果需要）
	if os.Getenv("DB_HOST") == "" {
		t.Skip("跳过测试：未设置数据库环境变量")
	}

	// 初始化数据库操作
	pg := NewPostgreSQLOperations()
	defer pg.Close()

	// 生成测试用户ID
	testUserID := uuid.New()
	fmt.Printf("测试用户ID: %s\n", testUserID.String())

	// 测试确保钱包存在
	err := pg.EnsureWalletExists(testUserID)
	if err != nil {
		t.Fatalf("确保钱包存在失败: %v", err)
	}

	// 验证钱包是否创建成功
	balance, err := pg.GetUserBalance(testUserID.String())
	if err != nil {
		t.Fatalf("获取用户余额失败: %v", err)
	}

	// 检查余额是否为500积分
	if balance != 500 {
		t.Errorf("期望余额为500积分，实际为%d积分", balance)
	}

	fmt.Printf("测试通过：钱包确保存在，余额为%d积分\n", balance)
}
