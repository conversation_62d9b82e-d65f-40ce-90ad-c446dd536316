package main

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
)

// VolcengineClient 是火山引擎API客户端的包装器
type VolcengineClient struct {
	client *arkruntime.Client
}

// ClientManager 管理不同类型的客户端实例
type ClientManager struct {
	arkClient *VolcengineClient
	arkOnce   sync.Once
	mu        sync.RWMutex
}

// 全局客户端管理器实例
var clientManager = &ClientManager{}

// getArkClient 线程安全地获取火山引擎客户端
func (cm *ClientManager) getArkClient() (*VolcengineClient, error) {
	var err error
	cm.arkOnce.Do(func() {
		apiKey := GetArkAPIKey()
		// 检查配置是否可用
		if apiKey == "" {
			err = errors.New("火山引擎API密钥不能为空")
			return
		}
		// 创建客户端
		client := arkruntime.NewClientWithApiKey(apiKey)
		cm.arkClient = &VolcengineClient{
			client: client,
		}
	})
	if err != nil {
		return nil, err
	}
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	if cm.arkClient == nil {
		return nil, errors.New("火山引擎客户端初始化失败")
	}
	return cm.arkClient, nil
}

// ResetClients 重置所有客户端（在配置更改时使用）
func (cm *ClientManager) ResetClients() {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.arkClient = nil
	cm.arkOnce = sync.Once{}
}

// GetClientForModel 线程安全地获取适合模型的客户端
// 使用 sync.Once 确保了初始化代码只在第一次调用时执行一次
func GetClientForModel(modelName string) (*VolcengineClient, error) {
	// 根据模型选择客户端
	if containsKey(ARK_MODELS_MAP, modelName) {
		return clientManager.getArkClient()
	}
	return nil, errors.New("无法为该模型创建客户端")
}

// ResetAllClients 重置所有客户端的公共接口
func ResetAllClients() {
	clientManager.ResetClients()
}

// 辅助函数：创建文本消息
func createTextMessage(role string, content string) *model.ChatCompletionMessage {
	return &model.ChatCompletionMessage{
		Role: role,
		Content: &model.ChatCompletionMessageContent{
			StringValue: &content,
		},
	}
}

// 辅助函数：创建多模态消息
func createMultiModalMessage(role string, parts []*model.ChatCompletionMessageContentPart) *model.ChatCompletionMessage {
	return &model.ChatCompletionMessage{
		Role: role,
		Content: &model.ChatCompletionMessageContent{
			ListValue: parts,
		},
	}
}

// 辅助函数：创建文本内容部分
func createTextPart(text string) *model.ChatCompletionMessageContentPart {
	return &model.ChatCompletionMessageContentPart{
		Type: model.ChatCompletionMessageContentPartTypeText,
		Text: text,
	}
}

// 辅助函数：创建图像内容部分
func createImagePart(imageURL string, detail model.ImageURLDetail) *model.ChatCompletionMessageContentPart {
	return &model.ChatCompletionMessageContentPart{
		Type: model.ChatCompletionMessageContentPartTypeImageURL,
		ImageURL: &model.ChatMessageImageURL{
			URL:    imageURL,
			Detail: detail,
		},
	}
}

// 发送聊天完成请求
func (c *VolcengineClient) CreateChatCompletion(
	ctx context.Context,
	modelName string,
	messages []*model.ChatCompletionMessage,
	temperature float32,
) (*model.ChatCompletionResponse, error) {
	// 创建请求
	request := model.CreateChatCompletionRequest{
		Model:       modelName,
		Messages:    messages,
		Temperature: &temperature,
	}
	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	// 发送请求
	response, err := c.client.CreateChatCompletion(timeoutCtx, request)
	if err != nil {
		return nil, fmt.Errorf("Volcengine API错误: %w", err)
	}
	return &response, nil
}

// 发送带有结构化输出的聊天完成请求
func (c *VolcengineClient) CreateChatCompletionWithResponseFormat(
	ctx context.Context,
	modelName string,
	messages []*model.ChatCompletionMessage,
	temperature float32,
	responseFormat *model.ResponseFormat,
	thinkingType model.ThinkingType,
) (*model.ChatCompletionResponse, error) {
	// 创建请求
	request := model.CreateChatCompletionRequest{
		Model:       modelName,
		Messages:    messages,
		Temperature: &temperature,
	}
	// 如果提供了响应格式，则设置
	if responseFormat != nil {
		request.ResponseFormat = responseFormat
	}
	// 如果提供了思考，则设置
	if thinkingType != "" {
		request.Thinking = &model.Thinking{Type: thinkingType}
	}
	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 60*time.Second)
	defer cancel()
	// 发送请求
	response, err := c.client.CreateChatCompletion(timeoutCtx, request)
	if err != nil {
		return nil, fmt.Errorf("Volcengine API错误: %w", err)
	}
	return &response, nil
}

// 发送带有图像的聊天完成请求
func (c *VolcengineClient) CreateChatCompletionWithImage(
	ctx context.Context,
	modelName string,
	systemPrompt string,
	userText string,
	imageURL string,
	temperature float32,
) (*model.ChatCompletionResponse, error) {
	// 创建消息数组
	var messages []*model.ChatCompletionMessage
	if systemPrompt != "" {
		// 创建系统消息
		messages = append(messages, createTextMessage(model.ChatMessageRoleSystem, systemPrompt))
	}
	// 创建用户消息（包含文本和图像）
	userParts := []*model.ChatCompletionMessageContentPart{
		createImagePart(imageURL, model.ImageURLDetailHigh),
		createTextPart(userText),
	}
	messages = append(messages, createMultiModalMessage(model.ChatMessageRoleUser, userParts))
	// 发送请求
	return c.CreateChatCompletion(ctx, modelName, messages, temperature)
}

func (c *VolcengineClient) CreateChatCompletionWithText(
	ctx context.Context,
	modelName string,
	systemPrompt string,
	userText string,
	temperature float32,
) (*model.ChatCompletionResponse, error) {
	// 创建消息数组
	messages := []*model.ChatCompletionMessage{
		createTextMessage(model.ChatMessageRoleSystem, systemPrompt),
		createTextMessage(model.ChatMessageRoleUser, userText),
	}
	// 发送请求
	return c.CreateChatCompletion(ctx, modelName, messages, temperature)
}

// 发送带有图像和结构化输出的聊天完成请求
func (c *VolcengineClient) CreateChatCompletionWithImageAndResponseFormat(
	ctx context.Context,
	modelName string,
	systemPrompt string,
	userText string,
	imageURL string,
	temperature float32,
	responseFormat *model.ResponseFormat,
	thinkingType model.ThinkingType,
) (*model.ChatCompletionResponse, error) {
	// 创建消息数组
	var messages []*model.ChatCompletionMessage
	if systemPrompt != "" {
		// 创建系统消息
		messages = append(messages, createTextMessage(model.ChatMessageRoleSystem, systemPrompt))
	}
	// 创建用户消息（包含文本和图像）
	userParts := []*model.ChatCompletionMessageContentPart{
		createImagePart(imageURL, model.ImageURLDetailHigh),
		createTextPart(userText),
	}
	messages = append(messages, createMultiModalMessage(model.ChatMessageRoleUser, userParts))
	// 发送请求
	return c.CreateChatCompletionWithResponseFormat(ctx, modelName, messages, temperature, responseFormat, thinkingType)
}

// 发送带有文本和结构化输出的聊天完成请求
func (c *VolcengineClient) CreateChatCompletionWithTextAndResponseFormat(
	ctx context.Context,
	modelName string,
	systemPrompt string,
	userText string,
	temperature float32,
	responseFormat *model.ResponseFormat,
	thinkingType model.ThinkingType,
) (*model.ChatCompletionResponse, error) {
	// 创建消息数组
	messages := []*model.ChatCompletionMessage{
		createTextMessage(model.ChatMessageRoleSystem, systemPrompt),
		createTextMessage(model.ChatMessageRoleUser, userText),
	}
	// 发送请求
	return c.CreateChatCompletionWithResponseFormat(ctx, modelName, messages, temperature, responseFormat, thinkingType)
}

func CreateChatCompletion(ctx context.Context, chatRequest ChatRequest) (*model.ChatCompletionResponse, error) {
	if chatRequest.ContentType == "text" {
		client, err := GetClientForModel(chatRequest.Model)
		if err != nil {
			return nil, err
		}
		res, err := client.CreateChatCompletionWithText(ctx, chatRequest.Model, chatRequest.PromptKey, chatRequest.Text, chatRequest.Temperature)
		if err != nil {
			return nil, err
		}
		return res, nil
	} else if chatRequest.ContentType == "image" {
		client, err := GetClientForModel(chatRequest.Model)
		if err != nil {
			return nil, err
		}
		res, err := client.CreateChatCompletionWithImage(ctx, chatRequest.Model, chatRequest.PromptKey, chatRequest.Text, chatRequest.Content, chatRequest.Temperature)
		if err != nil {
			return nil, err
		}
		return res, nil
	}
	return nil, errors.New("不支持的ContentType")
}
