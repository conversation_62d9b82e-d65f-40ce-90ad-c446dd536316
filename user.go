package main

import (

	// 添加导入
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/google/uuid"
	"github.com/supabase-community/auth-go/types"
)

// AdminUserListResponse 定义了管理员获取用户列表的响应结构
type AdminUserListResponse struct {
	Success bool   `json:"success"`
	Users   []User `json:"users"`
	Count   int    `json:"count"`
	Total   int    `json:"total"` // 总用户数，用于分页
}

// HandleAdminGetUserList 处理管理员获取用户列表的请求
func (a *AuthClient) HandleAdminGetUserList(w http.ResponseWriter, r *http.Request) {
	// 只接受GET请求
	if r.Method != http.MethodGet {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	requestUser, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(requestUser.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 获取分页参数
	limit := 50
	offset := 0
	if limitStr := r.URL.Query().Get("limit"); limitStr != "" {
		if limitVal, err := strconv.Atoi(limitStr); err == nil && limitVal > 0 {
			limit = limitVal
		}
	}
	if offsetStr := r.URL.Query().Get("offset"); offsetStr != "" {
		if offsetVal, err := strconv.Atoi(offsetStr); err == nil && offsetVal >= 0 {
			offset = offsetVal
		}
	}
	// 获取用户列表
	users, err := a.GetUsers(limit, offset)
	if err != nil {
		http.Error(w, "获取用户列表失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 获取用户总数
	totalUsers, err := a.GetUserCount()
	if err != nil {
		http.Error(w, "获取用户总数失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回用户列表
	resp := AdminUserListResponse{
		Success: true,
		Users:   users,
		Count:   len(users),
		Total:   totalUsers,
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// User 定义了用户信息的结构体，用于API响应
type User struct {
	ID           uuid.UUID `json:"id"`
	Email        string    `json:"email"`
	CreatedAt    int64     `json:"created_at"`
	LastSignInAt int64     `json:"last_sign_in_at"`
}

// GetUsers 从数据库获取用户列表，支持分页
func (a *AuthClient) GetUsers(limit, offset int) ([]User, error) {
	// 获取用户列表
	res, err := a.client.AdminListUsers()
	if err != nil {
		LogError("获取用户列表失败: %v", err)
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}
	users := res.Users
	// 转换用户数据
	var result []User
	for _, user := range users {
		result = append(result, User{
			ID:        user.ID,
			Email:     user.Email,
			CreatedAt: user.CreatedAt.UTC().Unix(),
			LastSignInAt: func() int64 {
				if user.LastSignInAt == nil {
					return 0
				}
				return user.LastSignInAt.UTC().Unix()
			}(),
		})
	}
	// 应用分页
	start := offset
	end := min(offset+limit, len(result))
	if start > len(result) {
		return []User{}, nil
	}
	return result[start:end], nil
}

// GetUserCount 获取用户总数
func (a *AuthClient) GetUserCount() (int, error) {
	// 获取用户列表
	res, err := a.client.AdminListUsers()
	if err != nil {
		LogError("获取用户列表失败: %v", err)
		return 0, fmt.Errorf("获取用户列表失败: %w", err)
	}
	return len(res.Users), nil
}

// AdminCreateUserRequest 定义了管理员创建用户的请求结构
type AdminCreateUserRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// AdminCreateUserResponse 定义了管理员创建用户的响应结构
type AdminCreateUserResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	UserID  string `json:"user_id,omitempty"`
}

// HandleAdminCreateUser 处理管理员创建用户的请求
func (a *AuthClient) HandleAdminCreateUser(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "方法不允许", http.StatusMethodNotAllowed)
		return
	}
	// 从上下文中获取用户信息
	requestUser, ok := UserFromContext(r.Context())
	if !ok {
		http.Error(w, "未授权", http.StatusUnauthorized)
		return
	}
	// 检查管理员权限
	if !isAdmin(requestUser.ID.String()) {
		http.Error(w, "权限不足", http.StatusForbidden)
		return
	}
	// 解析请求体
	var req AdminCreateUserRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "请求格式错误", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" || req.Password == "" {
		http.Error(w, "邮箱和密码不能为空", http.StatusBadRequest)
		return
	}
	// 创建用户
	user, err := a.AdminCreateUser(req.Email, req.Password)
	if err != nil {
		http.Error(w, "创建用户失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	// 返回响应
	resp := AdminCreateUserResponse{
		Success: true,
		Message: "用户创建成功",
		UserID:  user.ID.String(),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// AdminCreateUser 管理员创建用户
func (a *AuthClient) AdminCreateUser(email, password string) (*User, error) {
	// 使用Supabase Admin API创建用户
	req := types.AdminCreateUserRequest{
		Email:        email,
		Password:     &password,
		EmailConfirm: true,
	}
	res, err := a.client.AdminCreateUser(req)
	if err != nil {
		LogError("创建用户失败: %v", err)
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}
	// 为新用户创建钱包并赠送500积分
	err = pg.CreateNewUserWallet(res.ID.String())
	if err != nil {
		LogError("为新用户创建钱包失败: %v", err)
		// 用户创建成功但钱包创建失败，仍然返回成功，钱包会在首次登录时创建
	}
	// 返回创建的用户信息
	return &User{
		ID:           res.ID,
		Email:        res.Email,
		CreatedAt:    res.CreatedAt.UTC().Unix(),
		LastSignInAt: 0,
	}, nil
}
