package main

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

var (
	// 全局数据库连接池
	db                     *sqlx.DB
	once                   sync.Once
	ErrInsufficientBalance = errors.New("insufficient balance")
)

// PostgreSQLOperations PostgreSQL数据库操作实现（使用sqlx）
type PostgreSQLOperations struct{}

// SystemPrompt 系统提示词结构体
type SystemPrompt struct {
	ID          int       `json:"id" db:"id"`
	PromptKey   string    `json:"prompt_key" db:"prompt_key"`
	PromptText  string    `json:"prompt_text" db:"prompt_text"`
	Description string    `json:"description" db:"description"`
	Category    string    `json:"category" db:"category"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// WebsiteConfig 网站配置结构体（PostgreSQL）
type WebsiteConfig struct {
	ID      string `json:"id" db:"id"`           // 网站业务ID
	Name    string `json:"name" db:"name"`       // 网站名称
	Url     string `json:"url" db:"url"`         // 网站URL
	Actions string `json:"actions" db:"actions"` // 操作步骤数组 (JSON格式)
}

// Wallet 钱包结构体
type Wallet struct {
	ID          int       `json:"id" db:"id"`
	UserID      string    `json:"user_id" db:"user_id"`
	PaidBalance int64     `json:"paid_balance" db:"paid_balance"` // 付费积分余额
	FreeBalance int64     `json:"free_balance" db:"free_balance"` // 赠送积分余额
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// 定义充值状态枚举
type ApprovalStatus string

const (
	StatusPending  ApprovalStatus = "pending"
	StatusApproved ApprovalStatus = "approved"
	StatusRejected ApprovalStatus = "rejected"
)

// RechargeRequest 对应 recharge_requests 表
type RechargeRequest struct {
	ID            int            `db:"id"`
	UserID        string         `db:"user_id"`
	Amount        int            `db:"amount"`
	OrderID       string         `db:"order_id"`
	Status        ApprovalStatus `db:"status"`
	PaymentMethod string         `db:"payment_method"`
	PaymentProof  sql.NullString `db:"payment_proof"`
	AdminNote     sql.NullString `db:"admin_note"`
	ProcessedBy   sql.NullString `db:"processed_by"`
	ProcessedAt   sql.NullTime   `db:"processed_at"`
	CreatedAt     time.Time      `db:"created_at"`
	UpdatedAt     time.Time      `db:"updated_at"`
}

// 实现 Valuer 接口，用于处理枚举值
func (s ApprovalStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// 实现 Scanner 接口，用于处理枚举值
func (s *ApprovalStatus) Scan(value any) error {
	if value == nil {
		*s = StatusPending
		return nil
	}
	str, ok := value.(string)
	if !ok {
		return errors.New("invalid approval status type")
	}
	switch str {
	case "pending", "approved", "rejected":
		*s = ApprovalStatus(str)
		return nil
	default:
		return fmt.Errorf("invalid approval status: %s", str)
	}
}

// TransactionSource 交易来源枚举
type TransactionSource string

const (
	TransactionSourceRecharge    TransactionSource = "RECHARGE"    // 付费充值
	TransactionSourceConsumption TransactionSource = "CONSUMPTION" // 业务消费
	TransactionSourceGiftSignup  TransactionSource = "GIFT_SIGNUP" // 注册赠送
	TransactionSourceGiftPromo   TransactionSource = "GIFT_PROMO"  // 活动赠送
	TransactionSourceRefund      TransactionSource = "REFUND"      // 退款
)

// Transaction 交易记录结构体
type Transaction struct {
	ID                   int64             `json:"id" db:"id"`                                         // 主键ID
	UserID               string            `json:"user_id" db:"user_id"`                               // 用户ID，对应于Supabase中的auth.users.id
	Source               TransactionSource `json:"source" db:"source"`                                 // 交易来源
	PaidPointsChange     int64             `json:"paid_points_change" db:"paid_points_change"`         // 付费积分变化量
	FreePointsChange     int64             `json:"free_points_change" db:"free_points_change"`         // 赠送积分变化量
	PaidBalanceAfter     int64             `json:"paid_balance_after" db:"paid_balance_after"`         // 交易后付费积分余额
	FreeBalanceAfter     int64             `json:"free_balance_after" db:"free_balance_after"`         // 交易后赠送积分余额
	Description          *string           `json:"description" db:"description"`                       // 交易描述（可选）
	RelatedRechargeID    *int              `json:"related_recharge_id" db:"related_recharge_id"`       // 关联充值申请ID
	RelatedConsumptionID *string           `json:"related_consumption_id" db:"related_consumption_id"` // 关联业务消费ID
	Metadata             *string           `json:"metadata" db:"metadata"`                             // 带有附加交易元数据的JSON字符串（可选）
	CreatedAt            time.Time         `json:"created_at" db:"created_at"`                         // 创建时间戳
}

// 初始化数据库连接池的函数
func initDB() (*sqlx.DB, error) {
	// 从环境变量中读取数据库连接信息
	dbHost := GetDBHost()
	dbPort := GetDBPort()
	dbUser := GetDBUser()
	dbPassword := GetDBPassword()
	dbName := GetDBName()
	if dbHost == "" {
		return nil, fmt.Errorf("DB_HOST environment variable not set")
	}
	// dsn (Data Source Name)
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)
	// 使用 sqlx.Connect，它会Ping数据库以验证连接
	conn, err := sqlx.Connect("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}
	// 配置连接池参数
	conn.SetMaxOpenConns(10)                 // 最大打开连接数
	conn.SetMaxIdleConns(5)                  // 最大空闲连接数
	conn.SetConnMaxLifetime(5 * time.Minute) // 连接最大存活时间
	conn.SetConnMaxIdleTime(1 * time.Minute) // 空闲连接超时
	return conn, nil
}

// getDBInstance 是一个线程安全的函数，用于获取DB实例
// sync.Once 确保了初始化代码只在第一次调用时执行一次
func GetDBInstance() (*sqlx.DB, error) {
	var err error
	once.Do(func() {
		db, err = initDB()
	})
	return db, err
}

// NewPostgreSQLOperations 创建PostgreSQL操作实例
func NewPostgreSQLOperations() *PostgreSQLOperations {
	return &PostgreSQLOperations{}
}

func (p *PostgreSQLOperations) Close() error {
	if db != nil {
		return db.Close()
	}
	return nil
}

// ==================== PostgreSQL操作方法 ====================

// GetActionsConfig 从PostgreSQL的website_configs表获取所有配置
func (p *PostgreSQLOperations) GetActionsConfig() ([]ConfigItem, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// 查询所有网站配置
	var configs []WebsiteConfig
	query := "SELECT id, name, url, actions FROM website_configs"
	err = dbconn.SelectContext(ctx, &configs, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get website configs: %v", err)
	}
	// 将WebsiteConfig转换为ConfigItem格式
	var configItems []ConfigItem
	for _, config := range configs {
		// 解析JSON格式的actions字段
		var actions []Action
		err = json.Unmarshal([]byte(config.Actions), &actions)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal actions for config %s: %v", config.ID, err)
		}
		// 创建ConfigItem
		configItem := ConfigItem{
			ID:      config.ID,
			Name:    config.Name,
			Url:     config.Url,
			Actions: actions,
		}
		configItems = append(configItems, configItem)
	}
	return configItems, nil
}

// GetSubjectPrompts 从PostgreSQL的system_prompts表获取科目提示词
func (p *PostgreSQLOperations) GetSubjectPrompts() ([]Subject, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var prompts []SystemPrompt
	query := "SELECT id, prompt_key, prompt_text, description, category, created_at, updated_at FROM system_prompts"
	err = dbconn.SelectContext(ctx, &prompts, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get system prompts: %v", err)
	}
	// 将SystemPrompt转换为Subject格式
	var subjects []Subject
	for _, prompt := range prompts {
		subject := Subject{
			SubjectID:   prompt.PromptKey,
			SubjectName: prompt.Description,
			PromptText:  prompt.PromptText,
		}
		subjects = append(subjects, subject)
	}
	return subjects, nil
}

func (p *PostgreSQLOperations) GetSystemPromptForSubject(subject string) (string, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return "", fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var prompt SystemPrompt
	query := "SELECT id, prompt_key, prompt_text, description, category, created_at, updated_at FROM system_prompts WHERE prompt_key = $1"
	err = dbconn.GetContext(ctx, &prompt, query, subject)
	if err != nil {
		return "", fmt.Errorf("failed to get system prompt: %v", err)
	}
	return prompt.PromptText, nil
}

// CreateRechargeRequest 创建新的充值申请
func (p *PostgreSQLOperations) CreateRechargeRequest(userID string, amount int, orderID string, paymentProof string) error {
	dbconn, err := GetDBInstance()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// 检查订单号是否已存在
	query := "SELECT EXISTS(SELECT 1 FROM recharge_requests WHERE order_id = $1)"
	var exists bool
	err = dbconn.GetContext(ctx, &exists, query, orderID)
	if err != nil {
		return fmt.Errorf("检查订单号失败: %w", err)
	}
	if exists {
		return errors.New("订单号已存在，请勿重复提交")
	}
	// 插入新的充值申请
	query = "INSERT INTO recharge_requests (user_id, amount, order_id, payment_proof, status, created_at) VALUES ($1, $2, $3, $4, $5, $6)"
	_, err = dbconn.ExecContext(ctx, query, userID, amount, orderID, paymentProof, "pending", time.Now())
	if err != nil {
		return fmt.Errorf("创建充值申请失败: %v", err)
	}
	return nil
}

// ProcessRechargeRequest 获取充值申请列表
func (p *PostgreSQLOperations) GetUserRechargeRequests(userID string, limit, offset int) ([]RechargeRequest, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var requests []RechargeRequest
	query := `SELECT id, user_id, amount, order_id, status, payment_method, payment_proof, admin_note, processed_by, processed_at, created_at, updated_at 
	FROM recharge_requests 
	WHERE user_id = $1
		ORDER BY created_at DESC LIMIT $2 OFFSET $3`
	err = dbconn.SelectContext(ctx, &requests, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("获取充值申请列表失败: %v", err)
	}
	return requests, nil
}

// GetRechargeRequests 获取充值申请列表
func (p *PostgreSQLOperations) GetRechargeRequests(status string, limit, offset int) ([]RechargeRequest, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var requests []RechargeRequest
	query := `SELECT id, user_id, amount, order_id, status, payment_method, payment_proof, admin_note, processed_by, processed_at, created_at, updated_at
	FROM recharge_requests
	WHERE status = $1
		ORDER BY created_at DESC
		LIMIT $2
		OFFSET $3`
	err = dbconn.SelectContext(ctx, &requests, query, status, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("获取充值申请列表失败: %v", err)
	}
	return requests, nil
}

// ProcessRechargeRequest 处理充值申请（批准或拒绝）
func (p *PostgreSQLOperations) ProcessRechargeRequest(requestID int64, adminID string, action string, note string, rmbAmount int) error {
	if action != "approve" && action != "reject" {
		return errors.New("无效的操作，必须是 'approve' 或 'reject'")
	}
	dbconn, err := GetDBInstance()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	// 开始事务
	tx, err := dbconn.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	now := time.Now()
	// 首先获取申请详情
	var req RechargeRequest
	err = tx.GetContext(ctx, &req,
		`SELECT id, user_id, amount, order_id, status FROM recharge_requests WHERE id = $1`,
		requestID,
	)
	if err != nil {
		return fmt.Errorf("查询充值申请失败: %v", err)
	}
	// 检查状态是否为待处理
	if req.Status != "pending" {
		return fmt.Errorf("此申请已处理，当前状态为: %s", req.Status)
	}
	// 如果管理员指定了充值金额，则使用管理员指定的金额
	originalAmount := req.Amount
	if rmbAmount > 0 && action == "approve" {
		req.Amount = rmbAmount
	}
	// 更新申请状态
	newStatus := "approved"
	if action == "reject" {
		newStatus = "rejected"
	}
	// 如果金额被修改，同时更新金额字段
	var updateQuery string
	var updateArgs []any
	if rmbAmount > 0 && action == "approve" && rmbAmount != originalAmount {
		updateQuery = `UPDATE recharge_requests
		SET status = $1, processed_by = $2, processed_at = $3, admin_note = $4, updated_at = $5, amount = $6
		WHERE id = $7`
		updateArgs = []any{newStatus, adminID, now, note, now, rmbAmount, requestID}
	} else {
		updateQuery = `UPDATE recharge_requests
		SET status = $1, processed_by = $2, processed_at = $3, admin_note = $4, updated_at = $5
		WHERE id = $6`
		updateArgs = []any{newStatus, adminID, now, note, now, requestID}
	}
	_, err = tx.ExecContext(ctx, updateQuery, updateArgs...)
	if err != nil {
		return fmt.Errorf("更新充值申请状态失败: %v", err)
	}
	// 如果批准，则给用户充值
	if action == "approve" {
		metadata := map[string]any{
			"rid":      req.ID,
			"aid":      adminID,
			"n":        note,
			"original": originalAmount,
		}
		// 将人民币金额转换为积分（1元 = 1000积分）
		pointsAmount := req.Amount * 1000
		// 构建描述信息，如果金额被修改，则在描述中说明
		var description string
		if originalAmount != req.Amount {
			description = fmt.Sprintf("rmb:%d(original:%d),%dp",
				req.Amount, originalAmount, pointsAmount)
		} else {
			description = fmt.Sprintf("rmb:%d,%dp", req.Amount, pointsAmount)
		}
		// 在同一个事务中执行充值操作
		// 1. 确保钱包存在
		_, err = tx.ExecContext(ctx,
			"INSERT INTO wallets (user_id, balance, created_at, updated_at) VALUES ($1, 500, $2, $3) ON CONFLICT (user_id) DO NOTHING",
			req.UserID, now, now,
		)
		if err != nil {
			return fmt.Errorf("确保钱包存在失败: %v", err)
		}
		// 2. 更新钱包余额
		var newBalance int
		err = tx.GetContext(ctx, &newBalance,
			"UPDATE wallets SET balance = balance + $1, updated_at = $2 WHERE user_id = $3 RETURNING balance",
			pointsAmount, now, req.UserID,
		)
		if err != nil {
			return fmt.Errorf("更新钱包余额失败: %v", err)
		}
		// 3. 序列化元数据
		var metadataJSON []byte
		metadataJSON, err = json.Marshal(metadata)
		if err != nil {
			return fmt.Errorf("序列化元数据失败: %v", err)
		}
		// 4. 记录交易
		_, err = tx.ExecContext(ctx,
			"INSERT INTO transactions (user_id, type, amount, balance_after, description, metadata, created_at) VALUES ($1, 'deposit', $2, $3, $4, $5, $6)",
			req.UserID, pointsAmount, newBalance, description, string(metadataJSON), now,
		)
		if err != nil {
			return fmt.Errorf("记录交易失败: %v", err)
		}
	}
	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}
	// 发送邮件通知（事务外执行，失败不影响主流程）
	if authClient != nil {
		switch action {
		case "approve":
			// 使用SMTP方式发送成功邮件
			err = authClient.SendRechargeSuccessEmailBySMTP(req.UserID, req.OrderID, req.Amount, req.Amount*1000)
			if err != nil {
				// 发送邮件失败不影响主流程，但记录错误
				LogError("发送充值成功通知失败: %v", err)
			}
		case "reject":
			// 使用SMTP方式发送失败邮件
			err = authClient.SendRechargeFailureEmailBySMTP(req.UserID, req.OrderID)
			if err != nil {
				// 发送邮件失败不影响主流程，但记录错误
				LogError("发送充值失败通知失败: %v", err)
			}
		}
	} else {
		LogWarn("警告: authClient未初始化，无法发送邮件通知")
	}
	return nil
}

// EnsureWalletExists 确保用户钱包存在，如果是新用户则创建钱包并赠送500积分
func (p *PostgreSQLOperations) EnsureWalletExists(userID uuid.UUID) error {
	dbconn, err := GetDBInstance()
	if err != nil {
		return fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 6*time.Second)
	defer cancel()
	// 检查钱包是否存在
	var exists bool
	query := "SELECT EXISTS(SELECT 1 FROM wallets WHERE user_id = $1)"
	err = dbconn.GetContext(ctx, &exists, query, userID)
	if err != nil {
		return fmt.Errorf("failed to check wallet existence: %v", err)
	}
	if !exists {
		// 新用户，需要在事务中创建钱包并赠送积分
		tx, err := dbconn.BeginTxx(ctx, nil)
		if err != nil {
			return fmt.Errorf("failed to begin transaction: %v", err)
		}
		defer func() {
			if err != nil {
				tx.Rollback()
			}
		}()
		now := time.Now()
		// 创建钱包：paid_balance 为0, free_balance 为500
		_, err = tx.ExecContext(ctx,
			"INSERT INTO wallets (user_id, paid_balance, free_balance, created_at, updated_at) VALUES ($1, 0, 500, $2, $3)",
			userID, now, now)
		if err != nil {
			return fmt.Errorf("failed to create wallet: %v", err)
		}
		// 在 transactions 表中插入注册赠送记录
		_, err = tx.ExecContext(ctx,
			`INSERT INTO transactions (user_id, source, paid_points_change, free_points_change,
			 paid_balance_after, free_balance_after, description, created_at)
			 VALUES ($1, 'GIFT_SIGNUP', 0, 500, 0, 500, 'New user registration gift: 500 points', $2)`,
			userID, now)
		if err != nil {
			return fmt.Errorf("failed to record signup gift transaction: %v", err)
		}
		// 提交事务
		if err = tx.Commit(); err != nil {
			return fmt.Errorf("failed to commit transaction: %v", err)
		}
	}
	return nil
}

// GetUserBalance 获取用户总余额（付费积分 + 赠送积分）
func (p *PostgreSQLOperations) GetUserBalance(userID string) (int, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return 0, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var totalBalance int
	query := "SELECT (paid_balance + free_balance) AS total_balance FROM wallets WHERE user_id = $1"
	err = dbconn.GetContext(ctx, &totalBalance, query, userID)
	if err != nil {
		return 0, fmt.Errorf("failed to get user balance: %v", err)
	}
	return totalBalance, nil
}

// GetUserInfo 获取用户信息
func (p *PostgreSQLOperations) GetUserInfo(userID string) (map[string]any, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// 获取用户详细余额
	var paidBalance, freeBalance int64
	query := "SELECT paid_balance, free_balance FROM wallets WHERE user_id = $1"
	err = dbconn.QueryRowContext(ctx, query, userID).Scan(&paidBalance, &freeBalance)
	if err != nil {
		return nil, fmt.Errorf("failed to get user balance: %v", err)
	}
	totalBalance := paidBalance + freeBalance
	// 获取已批准的充值总额
	var totalRecharge int
	query = "SELECT COALESCE(SUM(amount), 0) FROM recharge_requests WHERE user_id = $1 AND status = 'approved'"
	err = dbconn.GetContext(ctx, &totalRecharge, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user total recharge: %v", err)
	}
	// 构建用户信息
	userInfo := map[string]any{
		"user_id":      userID,
		"balance":      totalBalance,
		"paid_balance": paidBalance,
		"free_balance": freeBalance,
		"recharge":     totalRecharge,
	}
	return userInfo, nil
}

// ManualAdjustBalance 管理员手动调整用户余额（默认调整赠送积分）
func (p *PostgreSQLOperations) ManualAdjustBalance(userID string, amount int, adminID string, reason string) (int, error) {
	return p.ManualAdjustBalanceDetailed(userID, 0, amount, adminID, reason)
}

// ManualAdjustBalanceDetailed 管理员手动调整用户余额（可分别调整付费积分和赠送积分）
func (p *PostgreSQLOperations) ManualAdjustBalanceDetailed(userID string, paidAmount, freeAmount int, adminID string, reason string) (int, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return 0, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 6*time.Second)
	defer cancel()
	// 开始事务
	tx, err := dbconn.BeginTxx(ctx, nil)
	if err != nil {
		return 0, fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	now := time.Now()
	// 检查钱包是否存在,如果不存在则提醒先注册并登录
	var exists bool
	query := "SELECT EXISTS(SELECT 1 FROM wallets WHERE user_id = $1)"
	err = dbconn.GetContext(ctx, &exists, query, userID)
	if err != nil {
		return 0, fmt.Errorf("failed to check wallet existence: %v", err)
	}
	if !exists {
		return 0, fmt.Errorf("user wallet does not exist, please register and login first")
	}
	// 更新钱包余额
	var newPaidBalance, newFreeBalance int64
	if paidAmount < 0 || freeAmount < 0 {
		// 扣款操作，需要检查余额是否足够
		err = tx.QueryRowContext(ctx,
			`UPDATE wallets
			 SET paid_balance = paid_balance + $1, free_balance = free_balance + $2, updated_at = $3
			 WHERE user_id = $4 AND paid_balance >= $5 AND free_balance >= $6
			 RETURNING paid_balance, free_balance`,
			paidAmount, freeAmount, now, userID, -paidAmount, -freeAmount,
		).Scan(&newPaidBalance, &newFreeBalance)
		if err != nil {
			return 0, fmt.Errorf("failed to update wallet balance for withdrawal: %v", err)
		}
	} else {
		// 充值操作
		err = tx.QueryRowContext(ctx,
			`UPDATE wallets
			 SET paid_balance = paid_balance + $1, free_balance = free_balance + $2, updated_at = $3
			 WHERE user_id = $4
			 RETURNING paid_balance, free_balance`,
			paidAmount, freeAmount, now, userID,
		).Scan(&newPaidBalance, &newFreeBalance)
		if err != nil {
			return 0, fmt.Errorf("failed to update wallet balance for recharge: %v", err)
		}
	}
	// 序列化元数据
	metadata := map[string]any{
		"aid":         adminID,
		"reason":      reason,
		"paid_amount": paidAmount,
		"free_amount": freeAmount,
	}
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		return 0, fmt.Errorf("failed to marshal metadata: %v", err)
	}
	// 记录交易
	_, err = tx.ExecContext(ctx,
		`INSERT INTO transactions (user_id, source, paid_points_change, free_points_change,
		 paid_balance_after, free_balance_after, description, metadata, created_at)
		 VALUES ($1, 'REFUND', $2, $3, $4, $5, $6, $7, $8)`,
		userID, paidAmount, freeAmount, newPaidBalance, newFreeBalance, reason, string(metadataJSON), now,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to record transaction: %v", err)
	}
	// 提交事务
	if err = tx.Commit(); err != nil {
		return 0, fmt.Errorf("failed to commit transaction: %v", err)
	}
	return int(newPaidBalance + newFreeBalance), nil
}

func (p *PostgreSQLOperations) GetTransactionRecords(userID string, txType string, startTime, endTime int64, limit, offset int) ([]Transaction, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var transactions []Transaction
	query := `SELECT id, user_id, type, amount, balance_after, description, metadata, created_at
			  FROM transactions WHERE 1=1`
	args := []any{}
	// 添加过滤条件
	paramIndex := 1
	if userID != "" {
		query += fmt.Sprintf(" AND user_id = $%d", paramIndex)
		args = append(args, userID)
		paramIndex++
	}
	if txType != "" {
		query += fmt.Sprintf(" AND type = $%d", paramIndex)
		args = append(args, txType)
		paramIndex++
	}
	if startTime > 0 {
		query += fmt.Sprintf(" AND created_at >= $%d", paramIndex)
		args = append(args, startTime)
		paramIndex++
	}
	if endTime > 0 {
		query += fmt.Sprintf(" AND created_at <= $%d", paramIndex)
		args = append(args, endTime)
		paramIndex++
	}
	// 添加排序和分页
	query += fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d OFFSET $%d", paramIndex, paramIndex+1)
	args = append(args, limit, offset)
	// 执行查询
	err = dbconn.SelectContext(ctx, &transactions, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction records: %v", err)
	}
	return transactions, nil
}

// GetUserTransactionRecords 获取用户交易记录
func (p *PostgreSQLOperations) GetUserTransactionRecords(userId string, limit int, offset int) ([]Transaction, error) {
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	var transactions []Transaction
	query := `SELECT id, user_id, type, amount, balance_after, description, metadata, created_at
			  FROM transactions WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3`
	err = dbconn.SelectContext(ctx, &transactions, query, userId, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction records: %v", err)
	}
	return transactions, nil
}

// DailyStatistics 表示每日充值统计
type DailyStatistics struct {
	Date          string `json:"date"`
	TotalAmount   int    `json:"total_amount"`   // 人民币金额
	RequestCount  int    `json:"request_count"`  // 充值请求数
	ApprovedCount int    `json:"approved_count"` // 充值成功数
	RejectedCount int    `json:"rejected_count"` // 充值失败数
	UpdatedAt     int64  `json:"updated_at"`     // 统计更新时间
}

func (p *PostgreSQLOperations) GetRechargeStatistics(startDate, endDate string) ([]DailyStatistics, error) {
	// TODO
	return []DailyStatistics{}, nil
}

// 原子性地检查余额并消费（优先消费赠送积分，再消费付费积分）
func (p *PostgreSQLOperations) CheckAndConsume(userId string, amount int, description string, metadata map[string]any) (map[string]any, error) {
	if amount <= 0 {
		return nil, errors.New("consumption amount must be positive")
	}
	var metadataJSON string
	if metadata != nil {
		jsonBytes, err := json.Marshal(metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal metadata to JSON: %w", err)
		}
		metadataJSON = string(jsonBytes)
	} else {
		metadataJSON = "{}"
	}
	dbconn, err := GetDBInstance()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// 使用CTE实现原子性消费，优先消费赠送积分
	atomicConsumptionSQL := `
		WITH current_balance AS (
			SELECT paid_balance, free_balance,
				   (paid_balance + free_balance) AS total_balance
			FROM wallets
			WHERE user_id = $1
		),
		consumption_plan AS (
			SELECT
				CASE
					WHEN total_balance < $2 THEN NULL  -- 余额不足
					WHEN free_balance >= $2 THEN $2    -- 赠送积分足够
					ELSE free_balance                  -- 部分使用赠送积分
				END AS free_consumed,
				CASE
					WHEN total_balance < $2 THEN NULL  -- 余额不足
					WHEN free_balance >= $2 THEN 0     -- 不需要付费积分
					ELSE $2 - free_balance             -- 需要的付费积分
				END AS paid_consumed
			FROM current_balance
		),
		updated_wallet AS (
			UPDATE wallets
			SET
				paid_balance = paid_balance - cp.paid_consumed,
				free_balance = free_balance - cp.free_consumed,
				updated_at = NOW()
			FROM consumption_plan cp
			WHERE user_id = $1 AND cp.free_consumed IS NOT NULL
			RETURNING user_id, paid_balance, free_balance,
					  (paid_balance + free_balance) AS total_balance_after
		)
		INSERT INTO transactions (
			user_id, source, paid_points_change, free_points_change,
			paid_balance_after, free_balance_after, description, metadata, created_at
		)
		SELECT
			uw.user_id, 'CONSUMPTION'::transaction_source,
			-COALESCE(cp.paid_consumed, 0), -COALESCE(cp.free_consumed, 0),
			uw.paid_balance, uw.free_balance, $3, $4, NOW()
		FROM updated_wallet uw, consumption_plan cp
		RETURNING (paid_balance_after + free_balance_after) AS total_balance_after;
	`

	var newTotalBalance int
	err = dbconn.QueryRowxContext(ctx, atomicConsumptionSQL, userId, amount, description, metadataJSON).Scan(&newTotalBalance)
	if err != nil {
		// 检查错误是否为 sql.ErrNoRows
		// 如果是，说明CTE没有返回行，即余额不足。
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrInsufficientBalance
		}
		// 如果是其他错误，则是数据库或连接问题
		return nil, fmt.Errorf("database error during consumption: %w", err)
	}

	return map[string]any{
		"success": true,
		"balance": newTotalBalance,
	}, nil
}
