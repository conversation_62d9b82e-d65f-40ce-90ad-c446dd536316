package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"
)

// LogLevel 定义日志级别
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
)

// LokiStream 表示Loki日志流结构
type LokiStream struct {
	Stream map[string]string `json:"stream"`
	Values [][]string        `json:"values"`
}

// LokiPayload 表示发送到Loki的完整负载
type LokiPayload struct {
	Streams []LokiStream `json:"streams"`
}

// Logger 封装日志发送功能
type Logger struct {
	logServerURL string
	appName      string
	jobName      string
	source       string
}

// NewLogger 创建新的日志实例
func NewLogger() *Logger {
	logServerURL := os.Getenv("LOG_SERVER_URL")
	if logServerURL == "" {
		logServerURL = "http://*********:9081/loki/api/v1/push" // 默认值
	}
	return &Logger{
		logServerURL: logServerURL,
		appName:      "aig-function",
		jobName:      "cloud-function-push",
		source:       "serverless-aig",
	}
}

// sendToLoki 发送日志到Loki服务器
func (l *Logger) sendToLoki(level LogLevel, message string) {
	if l.logServerURL == "" {
		log.Printf("[%s] %s", level, message)
		return
	}
	timestamp := strconv.FormatInt(time.Now().UnixNano(), 10)
	payload := LokiPayload{
		Streams: []LokiStream{
			{
				Stream: map[string]string{
					"job":    l.jobName,
					"app":    l.appName,
					"level":  string(level),
					"source": l.source,
				},
				Values: [][]string{
					{timestamp, message},
				},
			},
		},
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		log.Printf("Error marshaling Loki payload: %v. Fallback Log: [%s] %s", err, level, message)
		return
	}
	req, err := http.NewRequest("POST", l.logServerURL, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("Error creating Loki request: %v. Fallback Log: [%s] %s", err, level, message)
		return
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Error sending log to Loki: %v. Fallback Log: [%s] %s", err, level, message)
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		log.Printf("Loki returned status %d. Fallback Log: [%s] %s", resp.StatusCode, level, message)
	}
}

// Debug 发送调试级别日志
func (l *Logger) Debug(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToLoki(LogLevelDebug, message)
}

// Info 发送信息级别日志
func (l *Logger) Info(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToLoki(LogLevelInfo, message)
}

// Warn 发送警告级别日志
func (l *Logger) Warn(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToLoki(LogLevelWarn, message)
}

// Error 发送错误级别日志
func (l *Logger) Error(format string, args ...any) {
	message := fmt.Sprintf(format, args...)
	l.sendToLoki(LogLevelError, message)
}

// 全局日志实例
var globalLogger = NewLogger()

// 全局日志函数，方便直接调用
func LogDebug(format string, args ...any) {
	globalLogger.Debug(format, args...)
}

func LogInfo(format string, args ...any) {
	globalLogger.Info(format, args...)
}

func LogWarn(format string, args ...any) {
	globalLogger.Warn(format, args...)
}

func LogError(format string, args ...any) {
	globalLogger.Error(format, args...)
}

// LogPrint 兼容原有的log.Print调用
func LogPrint(args ...any) {
	message := fmt.Sprint(args...)
	globalLogger.Info("%s", message)
}

// LogPrintf 兼容原有的log.Printf调用
func LogPrintf(format string, args ...any) {
	globalLogger.Info(format, args...)
}

// LogPrintln 兼容原有的log.Println调用
func LogPrintln(args ...any) {
	message := fmt.Sprintln(args...)
	globalLogger.Info("%s", message)
}
