package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"github.com/supabase-community/auth-go"
	"github.com/supabase-community/auth-go/types"
	"github.com/wneessen/go-mail"
)

// AuthClient 是Supabase身份验证客户端的包装器
type AuthClient struct {
	client auth.Client
}

// LoginRequest 表示登录请求的结构
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// RegisterRequest 表示注册请求的结构
type RegisterRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// RegisterResponse 表示注册请求的响应结构
type RegisterResponse struct {
	Code    int    `json:"code"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// Action represents a single action in the configuration
type Action struct {
	Index    int    `json:"index"`
	Type     string `json:"type"`
	Selector string `json:"selector"`
	Value    string `json:"value"`
}

// ConfigItem represents a single configuration item
type ConfigItem struct {
	ID      string   `json:"id"`
	Name    string   `json:"name"`
	Url     string   `json:"url"`
	Actions []Action `json:"actions"`
}

// Subject represents a single subject in the configuration
type Subject struct {
	SubjectID   string `json:"subject_id"`
	SubjectName string `json:"subject_name"`
	PromptText  string `json:"prompt_text"`
}

// LoginResponse represents the response from the login API
type LoginResponse struct {
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresIn    int          `json:"expires_in"`
	ExpiresAt    int64        `json:"expires_at"`
	Config       []ConfigItem `json:"config"`
	Subjects     []Subject    `json:"subjects"`
}

// NewAuthClient 创建一个新的AuthClient实例
func NewAuthClient(config SupabaseConfig) (*AuthClient, error) {
	if config.URL == "" || config.Key == "" {
		return nil, errors.New("Supabase URL和Key不能为空")
	}
	// 初始化Supabase Auth客户端
	client := auth.New(config.ID, config.Key)
	client = client.WithCustomAuthURL(config.URL + "/auth/v1")
	if serviceMode == ServiceModeAdmin {
		client = client.WithToken(config.Key)
	}
	return &AuthClient{
		client: client,
	}, nil
}

// Login 使用电子邮件和密码登录
func (a *AuthClient) Login(email, password string) (*LoginResponse, error) {
	// 调用Supabase Auth API进行登录
	resp, err := a.client.SignInWithEmailPassword(email, password)
	if err != nil {
		return nil, fmt.Errorf("登录失败: %v", err)
	}
	var config []ConfigItem
	actionsConfig, err := pg.GetActionsConfig()
	if err != nil || len(actionsConfig) == 0 {
		LogError("无法获取配置信息")
	} else {
		config = actionsConfig
	}
	var subjects []Subject
	subjectPrompts, err := pg.GetSubjectPrompts()
	if err != nil || len(subjectPrompts) == 0 {
		LogError("无法获取科目列表")
	} else {
		subjects = subjectPrompts
	}
	// 检查用户钱包是否存在
	err = pg.EnsureWalletExists(resp.User.ID)
	if err != nil {
		return nil, fmt.Errorf("登录失败: %v", err)
	}
	// 构建响应
	return &LoginResponse{
		AccessToken:  resp.Session.AccessToken,
		RefreshToken: resp.Session.RefreshToken,
		ExpiresIn:    resp.Session.ExpiresIn,
		ExpiresAt:    resp.Session.ExpiresAt,
		Config:       config,
		Subjects:     subjects,
	}, nil
}

// VerifyToken 验证访问令牌
func (a *AuthClient) VerifyToken(token string) (*types.UserResponse, error) {
	// 使用提供的令牌创建一个新的客户端
	authedClient := a.client.WithToken(token)
	// 获取用户信息
	user, err := authedClient.GetUser()
	if err != nil {
		return nil, err
	}
	return user, nil
}

// RefreshToken 刷新访问令牌
func (a *AuthClient) RefreshToken(refreshToken string) (*LoginResponse, error) {
	// 调用Supabase Auth API进行令牌刷新
	resp, err := a.client.RefreshToken(refreshToken)
	if err != nil {
		return nil, err
	}
	var config []ConfigItem
	actionsConfig, err := pg.GetActionsConfig()
	if err != nil {
		LogError("无法获取配置信息")
	} else {
		config = actionsConfig
	}
	var subjects []Subject
	subjectPrompts, err := pg.GetSubjectPrompts()
	if err != nil || len(subjectPrompts) == 0 {
		LogError("无法获取科目列表")
	} else {
		subjects = subjectPrompts
	}
	// 构建响应
	return &LoginResponse{
		AccessToken:  resp.Session.AccessToken,
		RefreshToken: resp.Session.RefreshToken,
		ExpiresIn:    resp.Session.ExpiresIn,
		ExpiresAt:    resp.Session.ExpiresAt,
		Config:       config,
		Subjects:     subjects,
	}, nil
}

// ExtractTokenFromHeader 从请求头中提取令牌
func ExtractTokenFromHeader(r *http.Request) (string, error) {
	// 从X-Access-Token头中获取令牌
	authtoken := r.Header.Get("X-Access-Token")
	if authtoken == "" {
		return "", errors.New("未提供认证令牌，请在X-Access-Token头中提供令牌")
	}
	parts := strings.Split(authtoken, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		return "", errors.New("X-Access-Token头格式无效")
	}
	return parts[1], nil
}

// AuthMiddleware 是一个验证用户身份的中间件
func (a *AuthClient) AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 从请求头中提取令牌
		token, err := ExtractTokenFromHeader(r)
		if err != nil {
			http.Error(w, "未授权: "+err.Error(), http.StatusUnauthorized)
			return
		}
		// 验证令牌
		user, err := a.VerifyToken(token)
		if err != nil {
			http.Error(w, "未授权: 无效的令牌", http.StatusUnauthorized)
			return
		}
		// 将用户信息添加到请求上下文中
		ctx := r.Context()
		ctx = WithUser(ctx, user)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// LoginHandler 处理登录请求
func (a *AuthClient) LoginHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 解析请求体
	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" || req.Password == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}
	// 尝试登录
	resp, err := a.Login(req.Email, req.Password)
	if err != nil {
		http.Error(w, "登录失败: "+err.Error(), http.StatusUnauthorized)
		return
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 在响应头中设置令牌，便于客户端提取
	w.Header().Set("X-Access-Token", resp.AccessToken)
	w.Header().Set("X-Refresh-Token", resp.RefreshToken)
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}

// RefreshTokenHandler 处理刷新令牌请求
func (a *AuthClient) RefreshTokenHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 从请求头中获取刷新令牌
	refreshToken := r.Header.Get("X-Refresh-Token")
	if refreshToken == "" {
		// 尝试从请求体中获取刷新令牌
		var req struct {
			RefreshToken string `json:"refresh_token"`
		}
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil || req.RefreshToken == "" {
			http.Error(w, "未提供刷新令牌", http.StatusBadRequest)
			return
		}
		refreshToken = req.RefreshToken
	}
	// 尝试刷新令牌
	resp, err := a.RefreshToken(refreshToken)
	if err != nil {
		http.Error(w, "刷新令牌失败: "+err.Error(), http.StatusUnauthorized)
		return
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 在响应头中设置令牌，便于客户端提取
	w.Header().Set("X-Access-Token", resp.AccessToken)
	w.Header().Set("X-Refresh-Token", resp.RefreshToken)
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}

// Register 注册新用户
func (a *AuthClient) Register(email, password string) (*RegisterResponse, error) {
	// 调用Supabase Auth API进行注册
	req := types.SignupRequest{
		Email:    email,
		Password: password,
	}
	res, err := a.client.Signup(req)
	if err != nil {
		return &RegisterResponse{
			Code:    http.StatusInternalServerError,
			Success: false,
			Message: fmt.Sprintf("注册失败: %v", err),
		}, nil
	}
	if res.Role != "authenticated" {
		return &RegisterResponse{
			Code:    http.StatusConflict,
			Success: false,
			Message: "注册失败: 邮箱已存在",
		}, nil
	}
	// 构建响应，只返回成功状态
	return &RegisterResponse{
		Code:    http.StatusCreated,
		Success: true,
		Message: "注册成功",
	}, nil
}

// RegisterHandler 处理注册请求
func (a *AuthClient) RegisterHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 解析请求体
	var req RegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" || req.Password == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}
	// 尝试注册
	resp, err := a.Register(req.Email, req.Password)
	if err != nil {
		http.Error(w, "注册失败: "+err.Error(), http.StatusBadRequest)
		return
	}
	if !resp.Success {
		http.Error(w, "注册失败: "+resp.Message, http.StatusConflict)
		return
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}

// RecoverRequest 表示密码恢复请求的结构
type RecoverRequest struct {
	Email string `json:"email"`
}

// RecoverResponse 表示密码恢复请求的响应结构
type RecoverResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// SendPasswordResetEmail 发送密码重置邮件
func (a *AuthClient) SendPasswordResetEmail(email string) (*RecoverResponse, error) {
	// 调用Supabase Auth API发送密码重置邮件
	req := types.RecoverRequest{
		Email: email,
	}
	err := a.client.Recover(req)
	if err != nil {
		return &RecoverResponse{
			Success: false,
			Message: fmt.Sprintf("发送密码重置邮件失败: %v", err),
		}, nil
	}
	// 构建响应，只返回成功状态
	return &RecoverResponse{
		Success: true,
		Message: "密码重置邮件已发送，请检查您的邮箱",
	}, nil
}

// RecoverHandler 处理密码恢复请求
func (a *AuthClient) RecoverHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 解析请求体
	var req RecoverRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}
	// 尝试发送密码重置邮件
	resp, err := a.SendPasswordResetEmail(req.Email)
	if err != nil {
		http.Error(w, "发送密码重置邮件失败: "+err.Error(), http.StatusBadRequest)
		return
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}

// UpdatePasswordRequest 表示更新密码请求的结构
type UpdatePasswordRequest struct {
	Password string `json:"password"`
}

// UpdatePasswordResponse 表示更新密码请求的响应结构
type UpdatePasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// UpdatePassword 更新用户密码
func (a *AuthClient) UpdatePassword(password string, token string) (*UpdatePasswordResponse, error) {
	// 使用提供的令牌创建一个新的客户端
	authedClient := a.client.WithToken(token)

	// 调用Supabase Auth API更新用户密码
	req := types.UpdateUserRequest{
		Password: &password,
	}
	_, err := authedClient.UpdateUser(req)
	if err != nil {
		return &UpdatePasswordResponse{
			Success: false,
			Message: fmt.Sprintf("更新密码失败: %v", err),
		}, nil
	}

	// 构建响应，只返回成功状态
	return &UpdatePasswordResponse{
		Success: true,
		Message: "密码已成功更新",
	}, nil
}

// VerifyRequest 表示验证令牌请求的结构
type VerifyRequest struct {
	Email string `json:"email"`
	Token string `json:"token"`
	Type  string `json:"type"`
}

// VerifyResponse 表示验证令牌响应的结构
type VerifyResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message,omitempty"`
	AccessToken  string `json:"access_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	ExpiresIn    int    `json:"expires_in,omitempty"`
	ExpiresAt    int64  `json:"expires_at,omitempty"`
}

// VerifyRecoveryToken 验证密码恢复令牌
func (a *AuthClient) VerifyRecoveryToken(email, token, tokenType string) (*VerifyResponse, error) {
	// 调用Supabase Auth API验证令牌
	var verificationType types.VerificationType
	switch tokenType {
	case "recovery":
		verificationType = types.VerificationTypeRecovery
	case "signup":
		verificationType = types.VerificationTypeSignup
	case "invite":
		verificationType = types.VerificationTypeInvite
	case "magiclink":
		verificationType = types.VerificationTypeMagiclink
	default:
		verificationType = types.VerificationTypeRecovery
	}
	req := types.VerifyForUserRequest{
		Email:      email,
		Token:      token,
		Type:       verificationType,
		RedirectTo: "http://localhost:3000",
	}
	resp, err := a.client.VerifyForUser(req)
	if err != nil {
		return &VerifyResponse{
			Success: false,
			Message: fmt.Sprintf("验证令牌失败: %v", err),
		}, nil
	}
	// 构建响应
	return &VerifyResponse{
		Success:      true,
		Message:      "令牌验证成功",
		AccessToken:  resp.AccessToken,
		RefreshToken: resp.RefreshToken,
		ExpiresIn:    resp.ExpiresIn,
		ExpiresAt:    resp.ExpiresAt,
	}, nil
}

// VerifyHandler 处理验证令牌请求
func (a *AuthClient) VerifyHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 解析请求体
	var req VerifyRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" || req.Token == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}
	// 如果未提供类型，默认为recovery
	if req.Type == "" {
		req.Type = "recovery"
	}
	// 尝试验证令牌
	resp, err := a.VerifyRecoveryToken(req.Email, req.Token, req.Type)
	if err != nil {
		http.Error(w, "验证令牌失败: "+err.Error(), http.StatusBadRequest)
		return
	}
	if !resp.Success {
		http.Error(w, resp.Message, http.StatusBadRequest)
		return
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 在响应头中设置令牌，便于客户端提取
	w.Header().Set("X-Access-Token", resp.AccessToken)
	if resp.RefreshToken != "" {
		w.Header().Set("X-Refresh-Token", resp.RefreshToken)
	}
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}

// UpdatePasswordHandler 处理更新密码请求
func (a *AuthClient) UpdatePasswordHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}

	// 从请求头中提取令牌
	token, err := ExtractTokenFromHeader(r)
	if err != nil {
		http.Error(w, "未授权: "+err.Error(), http.StatusUnauthorized)
		return
	}

	// 解析请求体
	var req UpdatePasswordRequest
	if err = json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}

	// 验证请求
	if req.Password == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}

	// 尝试更新密码
	resp, err := a.UpdatePassword(req.Password, token)
	if err != nil {
		http.Error(w, "更新密码失败: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}

// 使用 go-mail 库发送的邮件
func (a *AuthClient) SendRechargeSuccessEmailBySMTP(uid, orderID string, rmbAmount, pointsAmount int) error {
	// 获取用户信息
	req := types.AdminGetUserRequest{
		UserID: uuid.MustParse(uid),
	}
	res, err := a.client.AdminGetUser(req)
	if err != nil {
		LogError("无法获取用户信息: %v", err)
		return err
	}
	// 获取SMTP配置
	smtpConfig := GetSMTPConfig()
	if smtpConfig.Host == "" || smtpConfig.Port == 0 || smtpConfig.User == "" || smtpConfig.Password == "" {
		return fmt.Errorf("SMTP配置不完整")
	}
	message := mail.NewMsg()
	if err = message.From(smtpConfig.From); err != nil {
		LogError("failed to set From address: %s", err)
		return fmt.Errorf("设置发件人失败: %w", err)
	}
	if err = message.To(res.Email); err != nil {
		LogError("failed to set To address: %s", err)
		return fmt.Errorf("设置收件人失败: %w", err)
	}
	message.Subject("山竹阅卷 - 充值成功通知")
	// TODO: 生成唯一退订Token
	// 构建HTML邮件内容
	htmlBody := fmt.Sprintf(`
		<html>
		<head>
			<meta charset="UTF-8">
			<title>充值成功通知</title>
		</head>
		<body>
			<div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
				<h2 style="color: #4CAF50;">充值成功通知</h2>
				<p>尊敬的用户：</p>
				<p>您的账户已成功充值 <strong>%d</strong> 元人民币，兑换 <strong>%d</strong> 积分。</p>
				<p>订单号：<strong>%s</strong></p>
				<p>感谢您对山竹阅卷的支持！</p>
				<p>此邮件为系统自动发送，请勿回复。</p>
				<div style="margin-top: 30px; padding-top: 10px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
					<p>山竹阅卷团队</p>
					<p>官方网站：<a href="https://shanzhulab.cn">https://shanzhulab.cn</a></p>
				</div>
			</div>
		</body>
		</html>
	`, rmbAmount, pointsAmount, orderID)
	// 设置邮件内容
	message.SetBodyString(mail.TypeTextHTML, htmlBody)
	// 创建SMTP客户端
	client, err := mail.NewClient(smtpConfig.Host,
		mail.WithPort(smtpConfig.Port),
		mail.WithSMTPAuth(mail.SMTPAuthPlain),
		mail.WithUsername(smtpConfig.User),
		mail.WithPassword(smtpConfig.Password),
		mail.WithSSL())
	if err != nil {
		LogError("failed to create mail client: %s", err)
		return fmt.Errorf("创建SMTP客户端失败: %w", err)
	}
	if err := client.DialAndSend(message); err != nil {
		LogError("failed to send mail: %s", err)
		return fmt.Errorf("发送邮件失败: %w", err)
	}
	return nil
}

func (a *AuthClient) SendRechargeFailureEmailBySMTP(uid string, orderID string) error {
	// 获取用户信息
	req := types.AdminGetUserRequest{
		UserID: uuid.MustParse(uid),
	}
	res, err := a.client.AdminGetUser(req)
	if err != nil {
		LogError("无法获取用户信息: %v", err)
		return err
	}
	// 获取SMTP配置
	smtpConfig := GetSMTPConfig()
	if smtpConfig.Host == "" || smtpConfig.Port == 0 || smtpConfig.User == "" || smtpConfig.Password == "" {
		return fmt.Errorf("SMTP配置不完整")
	}
	message := mail.NewMsg()
	if err = message.From(smtpConfig.From); err != nil {
		LogError("failed to set From address: %s", err)
		return fmt.Errorf("设置发件人失败: %w", err)
	}
	if err = message.To(res.Email); err != nil {
		LogError("failed to set To address: %s", err)
		return fmt.Errorf("设置收件人失败: %w", err)
	}
	message.Subject("山竹阅卷 - 充值失败通知")
	// 构建HTML邮件内容
	htmlBody := fmt.Sprintf(`
		<html>
		<head>
			<meta charset="UTF-8">
			<title>充值失败通知</title>
		</head>
		<body>
			<div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
				<h2 style="color: #FF0000;">充值失败通知</h2>
				<p>尊敬的用户：</p>
				<p>您的订单充值失败，订单号如下：</p>
				<div style="font-size: 24px; letter-spacing: 2px; padding: 15px; background: #f5f5f5; text-align: center; margin: 20px 0;">%s</div>
				<p>请检查您的充值订单信息是否正确，并重新提交。</p>
				<p>如果您认为这是由于系统错误导致的，请联系管理员以获取更多帮助。</p>
				<p>感谢您对山竹阅卷的支持！</p>
				<p>此邮件为系统自动发送，请勿回复。</p>
				<div style="margin-top: 30px; padding-top: 10px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
					<p>山竹阅卷团队</p>
					<p>官方网站：<a href="https://shanzhulab.cn">https://shanzhulab.cn</a></p>
				</div>
			</div>
		</body>
		</html>
	`, orderID)
	// 设置邮件内容
	message.SetBodyString(mail.TypeTextHTML, htmlBody)
	// 创建SMTP客户端
	client, err := mail.NewClient(smtpConfig.Host,
		mail.WithPort(smtpConfig.Port),
		mail.WithSMTPAuth(mail.SMTPAuthPlain),
		mail.WithUsername(smtpConfig.User),
		mail.WithPassword(smtpConfig.Password),
		mail.WithSSL())
	if err != nil {
		LogError("failed to create mail client: %s", err)
		return fmt.Errorf("创建SMTP客户端失败: %w", err)
	}
	if err := client.DialAndSend(message); err != nil {
		LogError("failed to send mail: %s", err)
		return fmt.Errorf("发送邮件失败: %w", err)
	}
	return nil
}

// EmailCodeRequest 表示发送邮箱验证码请求的结构
type EmailCodeRequest struct {
	Email string `json:"email"`
}

// EmailCodeResponse 表示发送邮箱验证码响应的结构
type EmailCodeResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// EmailLoginRequest 表示邮箱验证码登录请求的结构
type EmailLoginRequest struct {
	Email string `json:"email"`
	Code  string `json:"code"`
}

// SendEmailCode 发送邮箱验证码
func (a *AuthClient) SendEmailCode(email string) (*EmailCodeResponse, error) {
	// 调用Supabase Auth API发送魔法链接邮件，但我们只使用其中的验证码
	req := types.OTPRequest{
		Email: email,
	}
	err := a.client.OTP(req)
	if err != nil {
		return &EmailCodeResponse{
			Success: false,
			Message: fmt.Sprintf("发送验证码失败: %v", err),
		}, nil
	}
	// 构建响应，只返回成功状态
	return &EmailCodeResponse{
		Success: true,
		Message: "验证码已发送到您的邮箱，请查收",
	}, nil
}

// EmailCodeLoginHandler 处理邮箱验证码登录请求
func (a *AuthClient) EmailCodeLoginHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 解析请求体
	var req EmailLoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" || req.Code == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}
	// 验证邮箱验证码并登录
	resp, err := a.VerifyRecoveryToken(req.Email, req.Code, "magiclink")
	if err != nil {
		http.Error(w, "验证码验证失败: "+err.Error(), http.StatusBadRequest)
		return
	}
	if !resp.Success {
		http.Error(w, resp.Message, http.StatusBadRequest)
		return
	}
	// 获取用户配置信息
	var config []ConfigItem
	actionsConfig, err := pg.GetActionsConfig()
	if err != nil || len(actionsConfig) == 0 {
		LogError("无法获取配置信息")
	} else {
		config = actionsConfig
	}
	// 获取科目信息
	var subjects []Subject
	subjectPrompts, err := pg.GetSubjectPrompts()
	if err != nil || len(subjectPrompts) == 0 {
		LogError("无法获取科目列表")
	} else {
		subjects = subjectPrompts
	}
	// 构建登录响应
	loginResp := LoginResponse{
		AccessToken:  resp.AccessToken,
		RefreshToken: resp.RefreshToken,
		ExpiresIn:    resp.ExpiresIn,
		ExpiresAt:    resp.ExpiresAt,
		Config:       config,
		Subjects:     subjects,
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	// 在响应头中设置令牌，便于客户端提取
	w.Header().Set("X-Access-Token", resp.AccessToken)
	if resp.RefreshToken != "" {
		w.Header().Set("X-Refresh-Token", resp.RefreshToken)
	}
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(loginResp)
}

// SendEmailCodeHandler 处理发送邮箱验证码请求
func (a *AuthClient) SendEmailCodeHandler(w http.ResponseWriter, r *http.Request) {
	// 只接受POST请求
	if r.Method != http.MethodPost {
		http.Error(w, "", http.StatusMethodNotAllowed)
		return
	}
	// 解析请求体
	var req EmailCodeRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "", http.StatusBadRequest)
		return
	}
	// 验证请求
	if req.Email == "" {
		http.Error(w, "illegal request", http.StatusBadRequest)
		return
	}
	// 发送验证码
	resp, err := a.SendEmailCode(req.Email)
	if err != nil {
		http.Error(w, "发送验证码失败: "+err.Error(), http.StatusBadRequest)
		return
	}
	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(resp)
}
